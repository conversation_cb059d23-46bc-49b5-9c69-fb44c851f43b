00:34:21.630 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 249008 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:34:21.634 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:34:22.657 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:34:22.658 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:34:22.658 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:34:22.696 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:34:23.257 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:34:23.506 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:34:23.828 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:34:23.829 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:34:24.157 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:34:24.185 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:34:24.186 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:34:24.265 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:34:24.271 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:34:24.273 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:34:24.274 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:34:24.274 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:34:24.275 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:34:24.275 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:34:24.275 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:34:24.276 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:34:24.278 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:34:24.317 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:34:24.432 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:34:24.435 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:34:24.437 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
00:34:24.438 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
00:34:24.442 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
00:34:24.443 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
00:34:39.010 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 249632 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:34:39.012 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:34:39.813 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:34:39.813 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:34:39.813 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:34:39.848 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:34:40.441 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:34:40.706 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:34:41.039 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:34:41.040 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:34:41.336 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:34:41.366 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:34:41.367 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:34:41.448 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:34:41.452 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:34:41.455 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:34:41.456 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:34:41.456 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:34:41.456 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:34:41.456 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:34:41.456 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:34:41.457 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:34:41.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:34:41.500 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:34:41.518 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.907 seconds (JVM running for 3.38)
00:35:08.129 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:35:38.210 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
00:35:38.211 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
00:35:38.211 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
00:35:38.338 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:35:38.338 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1664] - 创建新JSON格式表格，总行数: 3, 总列数: 8
00:35:38.373 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 890px (13350twips)
00:35:38.373 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1688] - 开始处理表头，表头行数: 2
00:35:38.411 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1700] - 表头处理完成，当前行索引: 2
00:35:38.411 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 197px (2955twips)
00:35:38.412 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1883] - 开始处理嵌套表格，父单元格内容: 详细技术要求
00:35:38.412 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1951] - 创建嵌套表格: 2行 x 3列
00:35:38.413 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1957] - 开始创建真正的嵌套表格
00:35:38.441 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1968] - 使用WordTableExample方式创建嵌套表格成功
00:35:38.448 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1929] - 嵌套表格处理完成
00:35:38.453 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2360] - 应用JSON格式表头合并单元格，数量: 7
00:35:38.454 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2403] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查项目'
00:35:38.457 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2403] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '技术要求'
00:35:38.458 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2403] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '检查结果'
00:35:38.458 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2403] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:35:38.459 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,984] - 应用水平合并: 行0, 列3-4, 跨度2列
00:35:38.461 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1025] - 水平合并完成: 行0, 列3-4
00:35:38.462 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2403] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '检查员'
00:35:38.463 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2403] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '组长'
00:35:38.464 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2403] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:35:38.519 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2865 bytes
00:35:38.525 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_003538.docx, 大小: 2865 bytes
00:38:46.514 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:38:46.517 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:38:50.813 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 255200 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:38:50.817 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:38:53.555 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:38:53.555 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:38:53.556 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:38:53.593 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:38:54.137 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:38:54.369 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:38:54.692 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:38:54.693 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:38:55.019 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:38:55.048 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:38:55.049 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:38:55.128 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:38:55.135 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:38:55.139 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:38:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:38:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:38:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:38:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:38:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:38:55.141 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:38:55.142 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:38:55.187 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:38:55.203 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.951 seconds (JVM running for 3.362)
00:39:07.713 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:39:07.758 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
00:39:07.759 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
00:39:07.759 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
00:39:08.007 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:39:08.007 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1664] - 创建新JSON格式表格，总行数: 2, 总列数: 3
00:39:08.061 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 550px (8250twips)
00:39:08.061 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1688] - 开始处理表头，表头行数: 1
00:39:08.117 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1700] - 表头处理完成，当前行索引: 1
00:39:08.118 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 197px (2955twips)
00:39:08.119 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1883] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
00:39:08.120 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1951] - 创建嵌套表格: 2行 x 3列
00:39:08.121 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1957] - 开始创建真正的嵌套表格
00:39:08.159 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSafeNestedTable,2283] - 使用安全方式创建嵌套表格: 2行 x 3列
00:39:08.166 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,2376] - 使用文本方式表示嵌套表格: 2行 x 3列
00:39:08.170 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,2413] - 嵌套表格文本内容添加完成
00:39:08.170 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1929] - 嵌套表格处理完成
00:39:08.252 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2806 bytes
00:39:08.264 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_003908.docx, 大小: 2806 bytes
08:05:18.781 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
08:05:18.781 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
08:05:18.782 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
08:05:18.784 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:05:18.785 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1664] - 创建新JSON格式表格，总行数: 2, 总列数: 3
08:05:18.786 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 550px (8250twips)
08:05:18.786 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1688] - 开始处理表头，表头行数: 1
08:05:18.789 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1700] - 表头处理完成，当前行索引: 1
08:05:18.790 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 197px (2955twips)
08:05:18.791 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1883] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
08:05:18.791 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1951] - 创建嵌套表格: 2行 x 3列
08:05:18.791 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1957] - 开始创建真正的嵌套表格
08:05:18.792 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createSafeNestedTable,2283] - 使用安全方式创建嵌套表格: 2行 x 3列
08:05:18.795 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,2376] - 使用文本方式表示嵌套表格: 2行 x 3列
08:05:18.799 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,2413] - 嵌套表格文本内容添加完成
08:05:18.800 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1929] - 嵌套表格处理完成
08:05:18.806 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2805 bytes
08:05:18.809 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_080518.docx, 大小: 2805 bytes
08:05:23.450 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
08:05:23.455 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
08:05:28.317 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 272822 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
08:05:28.318 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
08:05:29.839 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
08:05:29.840 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:05:29.841 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
08:05:29.883 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:05:30.510 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:05:30.762 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:05:31.131 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
08:05:31.132 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
08:05:31.466 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
08:05:31.495 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
08:05:31.496 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
08:05:31.575 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
08:05:31.580 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
08:05:31.582 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
08:05:31.583 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
08:05:31.583 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
08:05:31.583 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
08:05:31.583 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
08:05:31.583 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
08:05:31.584 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
08:05:31.585 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
08:05:31.625 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
08:05:31.646 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.894 seconds (JVM running for 5.008)
08:05:38.934 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:05:38.979 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
08:05:38.980 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
08:05:38.980 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
08:05:39.158 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:05:39.159 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1664] - 创建新JSON格式表格，总行数: 2, 总列数: 3
08:05:39.207 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 550px (8250twips)
08:05:39.207 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1688] - 开始处理表头，表头行数: 1
08:05:39.250 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1700] - 表头处理完成，当前行索引: 1
08:05:39.250 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 197px (2955twips)
08:07:00.326 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1883] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
08:08:28.294 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1951] - 创建嵌套表格: 2行 x 3列
08:08:30.274 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1957] - 开始创建真正的嵌套表格
08:09:16.466 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSafeNestedTable,2283] - 使用安全方式创建嵌套表格: 2行 x 3列
08:09:25.331 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,2376] - 使用文本方式表示嵌套表格: 2行 x 3列
08:09:25.336 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,2413] - 嵌套表格文本内容添加完成
08:09:25.336 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1929] - 嵌套表格处理完成
08:09:25.416 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2806 bytes
08:09:25.424 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_080925.docx, 大小: 2806 bytes
08:25:06.649 [http-nio-9550-exec-4] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,91] - 接收到导出全部页面请求，车辆ID: 0822
08:25:06.650 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0822, 导出类型: all_pages
08:25:06.650 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportAllPages,82] - 导出全部页面，车辆ID: 0822
08:25:06.653 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,146] - 查询检验记录数据，车辆ID: 0822
08:25:06.653 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,197] - 查询到检验记录数据 4 条
08:25:06.653 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,741] - 开始导出多页面文档，页面数量: 3
08:25:06.657 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,762] - 处理第1页数据，页面名称: 第1页
08:25:06.658 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,666] - 页面1构建了9行数据
08:25:06.658 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,762] - 处理第2页数据，页面名称: 第2页
08:25:06.658 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,666] - 页面2构建了5行数据
08:25:06.658 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,762] - 处理第3页数据，页面名称: 第3页
08:25:06.659 [http-nio-9550-exec-4] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,666] - 页面3构建了8行数据
08:25:06.659 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWordWithPageBreaks,2682] - 开始导出支持分页符的JSON格式Word文档，表格标题: 检验记录表
08:25:06.661 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:25:06.665 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2716] - 创建支持分页符的JSON格式表格，表头行数: 2, 总列数: 8
08:25:06.665 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [splitRowsByPageBreaks,2794] - 数据行按分页符分割完成，共3页
08:25:06.670 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 880px (13200twips)
08:25:06.679 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.680 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.682 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.684 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.685 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 151px (2265twips)
08:25:06.685 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [insertMixedContent,637] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
08:25:06.687 [http-nio-9550-exec-4] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
08:25:06.928 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 51px (765twips)
08:25:06.929 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 51px (765twips)
08:25:06.930 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 51px (765twips)
08:25:06.931 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 51px (765twips)
08:25:06.931 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2466] - 应用JSON格式表头合并单元格，数量: 7
08:25:06.932 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:25:06.934 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:25:06.935 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:25:06.936 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:25:06.936 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,984] - 应用水平合并: 行0, 列3-4, 跨度2列
08:25:06.938 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1025] - 水平合并完成: 行0, 列3-4
08:25:06.939 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:25:06.939 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:25:06.940 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:25:06.940 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2757] - 第1页表格创建完成，数据行数: 9
08:25:06.943 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 880px (13200twips)
08:25:06.948 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.949 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.949 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.950 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.951 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 120px (1800twips)
08:25:06.952 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2466] - 应用JSON格式表头合并单元格，数量: 7
08:25:06.953 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:25:06.954 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:25:06.955 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:25:06.956 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:25:06.956 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,984] - 应用水平合并: 行0, 列3-4, 跨度2列
08:25:06.956 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1025] - 水平合并完成: 行0, 列3-4
08:25:06.956 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:25:06.957 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:25:06.957 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:25:06.958 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2757] - 第2页表格创建完成，数据行数: 5
08:25:06.959 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 880px (13200twips)
08:25:06.963 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.964 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.964 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.965 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 30px (450twips)
08:25:06.965 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 51px (765twips)
08:25:06.966 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 51px (765twips)
08:25:06.967 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 51px (765twips)
08:25:06.967 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 51px (765twips)
08:25:06.968 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2466] - 应用JSON格式表头合并单元格，数量: 7
08:25:06.968 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:25:06.969 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:25:06.969 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:25:06.970 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:25:06.970 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,984] - 应用水平合并: 行0, 列3-4, 跨度2列
08:25:06.970 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1025] - 水平合并完成: 行0, 列3-4
08:25:06.971 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:25:06.971 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:25:06.971 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2509] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:25:06.972 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2757] - 第3页表格创建完成，数据行数: 8
08:25:06.983 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWordWithPageBreaks,2703] - 支持分页符的JSON格式Word文档导出完成，文件大小: 3764 bytes
08:25:06.986 [http-nio-9550-exec-4] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,114] - 检验记录全部页面导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E5%85%A8%E9%83%A8%E9%A1%B5%E9%9D%A2_20250826_082506.docx, 大小: 3764 bytes
08:28:21.539 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
08:28:21.540 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
08:28:21.542 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
08:28:21.543 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
08:37:36.033 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
08:37:36.033 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
08:37:36.036 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
08:37:36.037 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
09:13:43.205 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:13:43.214 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:13:50.573 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 354257 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:13:50.575 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:13:51.565 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:13:51.565 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:13:51.566 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:13:51.605 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:13:52.195 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:13:52.435 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:13:52.796 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:13:52.798 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:13:53.132 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:13:53.160 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:13:53.162 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:13:53.243 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:13:53.248 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:13:53.251 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:13:53.251 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:13:53.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:13:53.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:13:53.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:13:53.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:13:53.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:13:53.254 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:13:53.293 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:13:53.309 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.145 seconds (JVM running for 3.854)
09:14:17.145 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:14:17.190 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
09:14:17.191 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
09:14:17.191 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
09:14:17.364 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:14:17.365 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1664] - 创建新JSON格式表格，总行数: 2, 总列数: 3
09:14:17.409 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 550px (8250twips)
09:14:17.409 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1688] - 开始处理表头，表头行数: 1
09:14:17.450 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1700] - 表头处理完成，当前行索引: 1
09:14:17.450 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 197px (2955twips)
09:14:22.780 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1883] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
09:14:27.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1951] - 创建嵌套表格: 2行 x 3列
09:14:27.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1957] - 开始创建真正的嵌套表格
09:14:27.120 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1993] - 使用WordTableExample方式创建嵌套表格成功
09:14:27.121 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1929] - 嵌套表格处理完成
09:14:27.182 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2730 bytes
09:14:27.188 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_091427.docx, 大小: 2730 bytes
09:18:47.923 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:18:47.926 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:18:52.645 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 360343 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:18:52.646 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:18:53.535 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:18:53.535 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:53.536 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:18:53.572 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:54.124 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:18:54.355 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:18:54.712 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:18:54.713 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:18:55.027 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:18:55.055 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:18:55.056 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:18:55.132 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:18:55.137 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:18:55.139 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:18:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:18:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:18:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:18:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:18:55.140 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:18:55.141 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:18:55.143 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:18:55.181 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:18:55.196 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.909 seconds (JVM running for 3.509)
09:19:00.377 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:19:00.430 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
09:19:00.431 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
09:19:00.432 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
09:19:00.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:19:00.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1664] - 创建新JSON格式表格，总行数: 2, 总列数: 3
09:19:00.651 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 550px (8250twips)
09:19:00.652 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1688] - 开始处理表头，表头行数: 1
09:19:00.690 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1700] - 表头处理完成，当前行索引: 1
09:19:00.691 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 197px (2955twips)
09:19:05.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1883] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
09:19:09.048 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1951] - 创建嵌套表格: 2行 x 3列
09:19:09.048 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1957] - 开始创建真正的嵌套表格
09:19:09.083 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1994] - 使用WordTableExample方式创建嵌套表格成功
09:19:09.083 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1929] - 嵌套表格处理完成
09:19:09.148 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2736 bytes
09:19:09.155 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_091909.docx, 大小: 2736 bytes
09:41:04.180 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:41:04.183 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:41:09.354 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 386105 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:41:09.356 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:41:11.897 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:41:11.898 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:41:11.898 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:41:11.937 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:41:12.527 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:41:12.779 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:41:13.123 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:41:13.124 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:41:13.432 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:41:13.460 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:41:13.461 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:41:13.537 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:41:13.544 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:41:13.546 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:41:13.547 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:41:13.547 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:41:13.547 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:41:13.547 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:41:13.547 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:41:13.548 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:41:13.549 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:41:13.592 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:41:13.608 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.065 seconds (JVM running for 3.712)
09:41:20.244 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:41:20.294 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
09:41:20.295 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
09:41:20.295 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
09:41:20.570 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:41:20.570 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1664] - 创建新JSON格式表格，总行数: 2, 总列数: 3
09:41:20.613 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1756] - 设置表格总宽度: 550px (8250twips)
09:41:20.614 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1688] - 开始处理表头，表头行数: 1
09:41:20.660 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1700] - 表头处理完成，当前行索引: 1
09:41:20.660 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1836] - 设置数据行高度: 197px (2955twips)
09:41:20.662 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1883] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
09:41:20.663 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1951] - 创建嵌套表格: 2行 x 3列
09:41:20.664 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1957] - 开始创建真正的嵌套表格
09:41:20.713 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,2002] - 使用WordTableExample方式创建嵌套表格成功
09:41:20.718 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1929] - 嵌套表格处理完成
09:41:20.783 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2710 bytes
09:41:20.789 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_094120.docx, 大小: 2710 bytes
09:51:42.101 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:51:42.103 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:51:46.933 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 398449 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:51:46.935 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:51:47.904 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:51:47.905 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:51:47.905 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:51:47.942 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:51:48.533 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:51:48.778 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:51:49.186 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:51:49.187 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:51:49.537 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:51:49.578 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:51:49.580 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:51:49.697 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:51:49.704 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:51:49.707 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:51:49.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:51:49.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:51:49.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:51:49.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:51:49.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:51:49.709 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:51:49.712 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:51:49.770 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:51:49.794 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.248 seconds (JVM running for 3.868)
09:52:04.873 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:52:04.921 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
09:52:04.922 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
09:52:04.922 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: 表格导出
09:52:05.104 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:52:05.105 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 2, 总列数: 3
09:52:05.150 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 550px (8250twips)
09:52:05.150 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 1
09:52:05.191 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 1
09:52:05.191 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 197px (2955twips)
09:52:05.192 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1885] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
09:52:05.193 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1953] - 创建嵌套表格: 2行 x 3列
09:52:05.193 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1959] - 开始创建真正的嵌套表格
09:52:05.228 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1931] - 嵌套表格处理完成
09:52:05.290 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 2711 bytes
09:52:05.296 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_095205.docx, 大小: 2711 bytes
09:52:48.384 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
09:52:48.385 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
09:52:48.385 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: 表格导出
09:52:48.386 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:52:48.387 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 2, 总列数: 3
09:52:48.388 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 550px (8250twips)
09:52:48.388 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 1
09:52:48.391 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 1
09:52:48.391 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 197px (2955twips)
09:52:48.392 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1885] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
09:52:48.392 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1953] - 创建嵌套表格: 2行 x 3列
09:52:48.392 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1959] - 开始创建真正的嵌套表格
09:54:08.512 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1931] - 嵌套表格处理完成
09:54:08.521 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 2711 bytes
09:54:08.524 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_095408.docx, 大小: 2711 bytes
09:54:11.469 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:54:11.472 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:56:08.120 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 403788 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:56:08.122 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:56:09.049 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:56:09.050 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:56:09.050 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:56:09.087 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:56:09.652 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:56:09.886 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:56:10.239 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:56:10.240 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:56:10.581 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:56:10.613 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:56:10.614 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:56:10.699 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:56:10.705 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:56:10.707 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:56:10.707 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:56:10.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:56:10.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:56:10.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:56:10.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:56:10.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:56:10.710 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:56:10.751 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:56:10.766 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.028 seconds (JVM running for 3.592)
09:56:17.994 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:56:18.041 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
09:56:18.042 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
09:56:18.042 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: 表格导出
09:56:18.230 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:56:18.230 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 2, 总列数: 3
09:56:18.274 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 550px (8250twips)
09:56:18.274 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 1
09:56:18.315 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 1
09:56:18.316 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 197px (2955twips)
09:56:18.317 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1885] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
09:56:18.318 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1953] - 创建嵌套表格: 2行 x 3列
09:56:18.318 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1959] - 开始创建真正的嵌套表格
09:56:18.352 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1999] - 使用WordTableExample方式创建嵌套表格成功
09:56:18.353 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1931] - 嵌套表格处理完成
09:56:18.411 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 2780 bytes
09:56:18.417 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_095618.docx, 大小: 2780 bytes
09:57:50.858 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
09:57:50.859 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
09:57:50.859 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: 表格导出
09:57:50.860 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:57:50.861 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 2, 总列数: 3
09:57:50.861 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 550px (8250twips)
09:57:50.862 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 1
09:57:50.864 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 1
09:57:50.864 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 197px (2955twips)
09:57:50.866 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1885] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
09:57:50.866 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1953] - 创建嵌套表格: 2行 x 3列
09:57:50.866 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1959] - 开始创建真正的嵌套表格
09:58:54.567 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1999] - 使用WordTableExample方式创建嵌套表格成功
09:58:54.568 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1931] - 嵌套表格处理完成
09:58:54.579 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 2781 bytes
09:58:54.583 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_095854.docx, 大小: 2781 bytes
10:04:11.151 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:04:11.154 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:04:16.000 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 413369 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
10:04:16.001 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:04:16.881 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
10:04:16.882 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:04:16.882 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:04:16.920 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:04:17.516 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:04:17.757 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:04:18.104 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:04:18.106 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:04:20.789 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:04:20.817 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:04:20.818 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:04:20.897 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:04:20.902 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:04:20.904 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:04:20.905 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:04:20.905 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:04:20.905 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:04:20.905 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:04:20.905 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:04:20.906 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:04:20.908 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:04:20.952 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
10:04:20.967 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.98 seconds (JVM running for 3.565)
10:04:28.854 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:04:28.906 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
10:04:28.907 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
10:04:28.907 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: 表格导出
10:04:29.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
10:04:29.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 2, 总列数: 3
10:04:29.131 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 550px (8250twips)
10:04:29.131 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 1
10:04:29.171 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 1
10:04:29.172 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 197px (2955twips)
10:04:29.172 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1885] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
10:04:29.173 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1953] - 创建嵌套表格: 2行 x 3列
10:04:29.173 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1959] - 开始创建真正的嵌套表格
10:04:29.207 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1931] - 嵌套表格处理完成
10:04:29.267 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 2696 bytes
10:04:29.272 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_100429.docx, 大小: 2696 bytes
10:06:31.144 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:06:31.147 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:06:35.804 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 416221 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
10:06:35.806 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:06:36.749 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
10:06:36.749 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:06:36.750 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:06:36.788 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:06:40.030 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:06:40.273 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:06:40.645 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:06:40.646 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:06:40.984 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:06:41.019 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:06:41.022 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:06:41.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:06:41.118 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:06:41.121 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:06:41.122 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:06:41.122 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:06:41.122 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:06:41.122 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:06:41.122 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:06:41.123 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:06:41.124 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:06:41.165 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
10:06:41.181 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.109 seconds (JVM running for 3.673)
10:06:46.368 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:06:46.410 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
10:06:46.411 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
10:06:46.411 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: 表格导出
10:06:46.590 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
10:06:46.590 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 2, 总列数: 3
10:06:46.640 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 550px (8250twips)
10:06:46.640 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 1
10:06:46.681 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 1
10:06:46.682 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 197px (2955twips)
10:06:46.683 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1885] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
10:06:46.684 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1953] - 创建嵌套表格: 2行 x 3列
10:06:46.684 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1959] - 开始创建真正的嵌套表格
10:06:46.721 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,2004] - 使用WordTableExample方式创建嵌套表格成功
10:06:46.722 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1931] - 嵌套表格处理完成
10:06:46.784 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 2773 bytes
10:06:46.790 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_100646.docx, 大小: 2773 bytes
10:51:49.462 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
10:51:49.463 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
10:51:49.463 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: 表格导出
10:51:49.465 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
10:51:49.465 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 3, 总列数: 8
10:51:49.467 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 890px (13350twips)
10:51:49.467 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 2
10:51:49.477 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 2
10:51:49.478 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 197px (2955twips)
10:51:49.479 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1885] - 开始处理嵌套表格，父单元格内容: 详细技术要求
10:51:49.479 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1953] - 创建嵌套表格: 2行 x 3列
10:51:49.479 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1959] - 开始创建真正的嵌套表格
10:51:49.481 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,2004] - 使用WordTableExample方式创建嵌套表格成功
10:51:49.481 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1931] - 嵌套表格处理完成
10:51:49.482 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2385] - 应用JSON格式表头合并单元格，数量: 7
10:51:49.482 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2428] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查项目'
10:51:49.486 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2428] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '技术要求'
10:51:49.487 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2428] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '检查结果'
10:51:49.488 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2428] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:51:49.488 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,986] - 应用水平合并: 行0, 列3-4, 跨度2列
10:51:49.490 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1027] - 水平合并完成: 行0, 列3-4
10:51:49.491 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2428] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '检查员'
10:51:49.491 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2428] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '组长'
10:51:49.492 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2428] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:51:49.500 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 2934 bytes
10:51:49.503 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250826_105149.docx, 大小: 2934 bytes
16:47:09.267 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 56369 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
16:47:09.270 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:47:10.278 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
16:47:10.279 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:47:10.279 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:47:10.318 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:47:10.898 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:47:11.156 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:47:11.517 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
16:47:11.518 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
16:47:11.816 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
16:47:11.844 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
16:47:11.845 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
16:47:11.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
16:47:11.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
16:47:11.925 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
16:47:11.925 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
16:47:11.925 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
16:47:11.926 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
16:47:11.926 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
16:47:11.926 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
16:47:11.926 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
16:47:11.927 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
16:47:11.964 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
16:47:11.978 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.162 seconds (JVM running for 3.737)
16:48:42.266 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:49:28.638 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
16:49:28.638 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 1
16:49:28.638 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: null
16:49:28.829 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
16:49:28.829 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:49:28.878 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 880px (13200twips)
16:49:28.879 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 2
16:49:28.934 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 2
16:49:28.934 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 51px (765twips)
16:49:28.935 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [insertMixedContent,637] - 插入混合内容，内容: 1.脱漆
2.冲洗__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:49:28.937 [http-nio-9550-exec-5] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:49:29.189 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2393] - 应用JSON格式表头合并单元格，数量: 7
16:49:29.190 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:49:29.192 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:49:29.192 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:49:29.193 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:49:29.193 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,986] - 应用水平合并: 行0, 列3-4, 跨度2列
16:49:29.195 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1027] - 水平合并完成: 行0, 列3-4
16:49:29.195 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:49:29.195 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:49:29.196 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:49:29.196 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2403] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
16:49:29.245 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 3002 bytes
16:49:29.251 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250826_164929.docx, 大小: 3002 bytes
16:49:59.006 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
16:49:59.006 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 1
16:49:59.006 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: null
16:49:59.008 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
16:49:59.008 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 4, 总列数: 8
16:49:59.009 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 880px (13200twips)
16:49:59.010 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 2
16:49:59.015 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 2
16:49:59.015 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 51px (765twips)
16:49:59.016 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [insertMixedContent,637] - 插入混合内容，内容: 1.脱漆
2.冲洗__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:49:59.017 [http-nio-9550-exec-6] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:49:59.088 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 51px (765twips)
16:49:59.089 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2393] - 应用JSON格式表头合并单元格，数量: 7
16:49:59.090 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:49:59.090 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:49:59.091 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:49:59.092 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:49:59.092 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,986] - 应用水平合并: 行0, 列3-4, 跨度2列
16:49:59.092 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1027] - 水平合并完成: 行0, 列3-4
16:49:59.093 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:49:59.093 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:49:59.093 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:49:59.093 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2403] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
16:49:59.093 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,2475] - 数据行合并使用绝对索引，不需要偏移: 2
16:49:59.094 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,2490] - 应用数据行合并: 行[2-3], 列[0-0], 跨行: 2, 跨列: 1, 内容: '脱漆'
16:49:59.099 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 3036 bytes
16:49:59.102 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250826_164959.docx, 大小: 3036 bytes
16:50:34.955 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
16:50:34.956 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 1
16:50:34.956 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: null
16:50:34.957 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
16:50:34.958 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 4, 总列数: 8
16:50:34.960 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 780px (11700twips)
16:50:34.961 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 2
16:50:34.964 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 2
16:50:34.965 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 51px (765twips)
16:50:34.966 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [insertMixedContent,637] - 插入混合内容，内容: 1.脱漆
2.冲洗__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:50:34.966 [http-nio-9550-exec-7] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:50:35.036 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 151px (2265twips)
16:50:35.036 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2393] - 应用JSON格式表头合并单元格，数量: 7
16:50:35.037 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:50:35.037 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:50:35.037 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:50:35.038 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:50:35.038 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,986] - 应用水平合并: 行0, 列3-4, 跨度2列
16:50:35.038 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1027] - 水平合并完成: 行0, 列3-4
16:50:35.038 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:50:35.038 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:50:35.039 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:50:35.039 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2403] - 应用JSON格式数据行合并单元格，数量: 1，表头偏移: 2
16:50:35.039 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,2475] - 数据行合并使用绝对索引，不需要偏移: 2
16:50:35.039 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,2490] - 应用数据行合并: 行[2-3], 列[0-0], 跨行: 2, 跨列: 1, 内容: '脱漆'
16:50:35.044 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 3038 bytes
16:50:35.046 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250826_165035.docx, 大小: 3038 bytes
16:56:03.430 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
16:56:03.431 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
16:56:03.431 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: null
16:56:03.432 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
16:56:03.432 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 4, 总列数: 8
16:56:03.433 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 960px (14400twips)
16:56:03.433 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 2
16:56:03.435 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 2
16:56:03.435 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 258px (3870twips)
16:56:03.436 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 258px (3870twips)
16:56:03.437 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2393] - 应用JSON格式表头合并单元格，数量: 7
16:56:03.437 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:56:03.438 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:56:03.438 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:56:03.439 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:56:03.439 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,986] - 应用水平合并: 行0, 列3-4, 跨度2列
16:56:03.439 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1027] - 水平合并完成: 行0, 列3-4
16:56:03.440 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:56:03.440 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:56:03.440 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:56:03.445 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 3012 bytes
16:56:03.447 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250826_165603.docx, 大小: 3012 bytes
17:04:36.834 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
17:04:36.835 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
17:04:36.835 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1576] - 开始导出新JSON格式Word文档，表格标题: null
17:04:36.836 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
17:04:36.836 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1666] - 创建新JSON格式表格，总行数: 4, 总列数: 8
17:04:36.837 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1758] - 设置表格总宽度: 960px (14400twips)
17:04:36.837 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1690] - 开始处理表头，表头行数: 2
17:04:36.839 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1702] - 表头处理完成，当前行索引: 2
17:04:36.840 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 258px (3870twips)
17:04:36.840 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1838] - 设置数据行高度: 258px (3870twips)
17:04:36.841 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2393] - 应用JSON格式表头合并单元格，数量: 7
17:04:36.841 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:04:36.841 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:04:36.842 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:04:36.842 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:04:36.842 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,986] - 应用水平合并: 行0, 列3-4, 跨度2列
17:04:36.842 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1027] - 水平合并完成: 行0, 列3-4
17:04:36.842 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:04:36.842 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:04:36.843 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2436] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:04:36.847 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1597] - 新JSON格式Word文档导出完成，文件大小: 3013 bytes
17:04:36.850 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250826_170436.docx, 大小: 3013 bytes
17:12:24.459 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:12:24.462 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
17:12:29.705 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 88461 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
17:12:29.708 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:12:30.516 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
17:12:30.516 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:12:30.517 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:12:30.552 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:12:31.024 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
17:12:31.233 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
17:12:31.553 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
17:12:31.554 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
17:12:31.827 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
17:12:31.852 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
17:12:31.853 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
17:12:31.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
17:12:31.927 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
17:12:31.929 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
17:12:31.929 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
17:12:31.929 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
17:12:31.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
17:12:31.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
17:12:31.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
17:12:31.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
17:12:31.931 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
17:12:31.971 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
17:12:31.986 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.623 seconds (JVM running for 2.993)
17:12:36.239 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:12:36.275 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
17:12:36.276 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
17:12:36.276 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1578] - 开始导出新JSON格式Word文档，表格标题: null
17:12:36.425 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,774] - 已设置文档为横向纸张
17:12:36.425 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1668] - 创建新JSON格式表格，总行数: 4, 总列数: 8
17:12:36.468 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1760] - 设置表格总宽度: 960px (14400twips)
17:12:36.468 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1692] - 开始处理表头，表头行数: 2
17:12:36.519 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1704] - 表头处理完成，当前行索引: 2
17:12:36.519 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1840] - 设置数据行高度: 258px (3870twips)
17:12:36.523 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1840] - 设置数据行高度: 258px (3870twips)
17:12:36.525 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2641] - 应用JSON格式表头合并单元格，数量: 7
17:12:36.527 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2684] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:12:36.529 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2684] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:12:36.529 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2684] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:12:36.530 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2684] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:12:36.530 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,988] - 应用水平合并: 行0, 列3-4, 跨度2列
17:12:36.531 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1029] - 水平合并完成: 行0, 列3-4
17:12:36.532 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2684] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:12:36.532 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2684] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:12:36.533 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2684] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:12:36.589 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1599] - 新JSON格式Word文档导出完成，文件大小: 3012 bytes
17:12:36.596 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250826_171236.docx, 大小: 3012 bytes
